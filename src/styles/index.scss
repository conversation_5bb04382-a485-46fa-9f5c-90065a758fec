// Required functions import
@import 'bootstrap/scss/functions';

// Optional variable overrides
@import 'variables';

// Import everything (this will import all components)
// @import 'bootstrap/scss/bootstrap';

// Required imports
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/variables-dark';
@import 'bootstrap/scss/maps';
@import 'bootstrap/scss/mixins';
@import 'bootstrap/scss/root';

// Optional components
@import 'bootstrap/scss/utilities';
@import 'bootstrap/scss/reboot';
@import 'bootstrap/scss/type';
@import 'bootstrap/scss/containers';
@import 'bootstrap/scss/images';
@import 'bootstrap/scss/nav';
// @import 'bootstrap/scss/accordion';
// @import 'bootstrap/scss/alert';
// @import 'bootstrap/scss/badge';
// @import 'bootstrap/scss/breadcrumb';
// @import 'bootstrap/scss/button-group';
// @import 'bootstrap/scss/buttons';
// @import 'bootstrap/scss/card';
// @import 'bootstrap/scss/carousel';
// @import 'bootstrap/scss/close';
// @import 'bootstrap/scss/dropdown';
// @import 'bootstrap/scss/forms';
@import 'bootstrap/scss/grid';
// @import 'bootstrap/scss/list-group';
// @import 'bootstrap/scss/modal';
// @import 'bootstrap/scss/navbar';
// @import 'bootstrap/scss/offcanvas';
// @import 'bootstrap/scss/pagination';
// @import 'bootstrap/scss/placeholders';
// @import 'bootstrap/scss/popover';
// @import 'bootstrap/scss/progress';
// @import 'bootstrap/scss/spinners';
// @import 'bootstrap/scss/tables';
// @import 'bootstrap/scss/toasts';
// @import 'bootstrap/scss/tooltip';
// @import 'bootstrap/scss/transitions';

// Optional helpers
@import 'bootstrap/scss/helpers';

// Optional utilities
@import 'bootstrap/scss/utilities/api';


// =================== Project styles ===================
// --- Common styles ---
@import "animations";
@import "fonts";
@import "typography";
@import "global";

// --- Components ---
@import "forms";

// --- Layouts ---
@import "header";
@import "footer";
// @import "maintenance";

// === 3rd party
// @import "gdpr";
// @import 'vanilla-cookieconsent/dist/cookieconsent.css';

@import "hax";