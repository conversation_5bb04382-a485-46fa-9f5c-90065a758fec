---
/* Layout options (ACF) */
import PortfolioSectionFull from "./portfolio-sections/full.astro" // full : full width, single image
import PortfolioSection_1_2_small from "./portfolio-sections/1_2_small.astro" // 1_2_small : 50% + small 4/12 x2
import PortfolioSection_1_2 from "./portfolio-sections/1_2.astro" // 1_2 : 4/12 + 50%
import PortfolioSection_1_1 from "./portfolio-sections/1_1.astro" // 1_1 : oversized, double image [square]
import PortfolioSectionPortrait from "./portfolio-sections/1_1_portrait.astro" // 1_1_portrait : oversized, multiple image [portrait]
import PortfolioSectionLandscape from "./portfolio-sections/1_1_landscape.astro" // 1_1_landscape : oversized, double image [landscape]
import PortfolioSectionVideo from "./portfolio-sections/video.astro" // video : Video
import { Image } from "astro:assets"

const {
    featuredImage,
    rows,
    website
} = Astro.props

const thumb = featuredImage.half || featuredImage.sourceUrl,
      thumb_alt = ( featuredImage.altText ) || featuredImage.title
---
<div class="shazza" data-ref="shazzaRef">
    <section class="grid-section no-counter lg:hidden">
        <div class="row default">
            <div class="shazza-cell shazza-cell-80 shazza-cell-landscape">
            <figure class="item" >
                <Image
                    width={1000}
                    height={500}
                    src={thumb}
                    alt={thumb_alt}
                />
            </figure>
            </div>
        </div>
    </section>

    {rows.map(( row, index ) => {
        const   layout = row.gridSectionLayout
        console.log(layout)
        const renderSwitch = function(row, index){
            switch (layout) {
            case 'full':  return (<PortfolioSectionFull row={row} />)
            case '1_2_small': return (<PortfolioSection_1_2_small row={row} rowIndex={index} />)
            case '1_2': return (<PortfolioSection_1_2 row={row} rowIndex={index} />)
            case '1_1': return (<PortfolioSection_1_1 row={row} rowIndex={index} />)
            case '1_1_portrait': return (<PortfolioSectionPortrait row={row} rowIndex={index} />)
            case '1_1_landscape': return (<PortfolioSectionLandscape row={row} rowIndex={index} />)
            case 'video': return (<PortfolioSectionVideo row={row} />)

            default: return (<h3>No layout match</h3>)
        }
        }
        return (
            <section
            class={`grid-section text-center${['1_1', '1_1_portrait', '1_1_landscape'].indexOf(row.gridSectionLayout) != -1 ? ' narrow-section' : ''}${row.gridSectionReverse ? ' reverse' : ''}`} >
                { (row.gridSectionLayout != 'video' && !row.gridSectionImages) && <p class="alert">No images for this section, please upload something!</p>}
                {(website && row.gridSectionVisit) &&
                <h4 class="section-visit-website">
                <a href={website} title="Visit the website" target="_blank">Visit the website</a>
                </h4>}

                <Fragment set:html={renderSwitch(row,index)}></Fragment>
            </section>
        )
    })}
</div>

<script >
    import { gsap, ScrollTrigger } from "../lib/gsap"
    gsap.registerPlugin(ScrollTrigger)

    const shazzaRef = document.querySelector('[data-ref="shazzaRef"]')
    console.log(shazzaRef)
    const ctx = gsap.context((self) => {
      const sections = self.selector('.grid-section');
      sections.forEach((section) => {
        // console.log(section)
        gsap.from(section.querySelectorAll('.shazza-cell'), {
            y: 50,
            opacity: 0,
            stagger: 0.1,
            scrollTrigger: {
                trigger: section,
                start: 'top bottom',
                end: 'top 80%',
                toggleActions: "restart none none reverse",
                // markers: true
            },
        });
      });
    }, shazzaRef);
</script>