---
import { Image } from "astro:assets";
import PhotoCredit from "./photo-credit.astro";
const {image} = Astro.props
let thumb = ( image.thumbnail ) ? image.thumbnail.sourceUrl  : image.image.half,
        thumb_alt = ( image.image.altText ) || image.image.title,
        thumb_credit = image["photo_credit"],
        thumb_credit_link = image["photo_credit_url"]
---
<figure class="item" >
    <Image
        width={1000}
        height={500}
        src={thumb}
        alt={thumb_alt}
    />
    {thumb_credit && <PhotoCredit prefix={'Photography by '} author={thumb_credit} url={thumb_credit_link} />}
</figure>