.main-header {
	position: fixed;
	top: -186px;
	left:0;
	right: 0;
	z-index: 15;
	line-height: 1;
	transition: top 0.5s ease-in-out;
	padding: 0.75rem 0;

	&.open {
		top:0;
	}

	.logo-wrapper {
		position: fixed;
		top: 0;
		z-index: 1;
		display: block;
		height: 1.5rem;
		margin: 0.75rem 0;
		width: 90px;

		embed,
		img {
			height: 1.5rem;
			position: relative;
			z-index: -1;
			display: block;
		}
	}

	a {
		line-height: 1rem;
		font-size: 0.8rem;
		letter-spacing: 2px;
		transition: color 0.5s ease;
		color: $light;
	}

	nav {
		text-align: center;
		font-family: inherit;

		a {
			display: block;
			text-align: right;
			margin: .75rem 0.5rem;
		}

	}

	#nav-trigger {
		position: relative;
		top: 1px;
		height: 1.5rem;
		margin: 0;

		&:before {
			content: '';
			display: inline-block;
			height: 1px;
			width: 1.5rem;
			position: absolute;
			top: 3px;
			right: 0;
		}
	}

	.left-menu {
		nav {
			text-align: left;
		}
	}
}

@include media-breakpoint-up(md) {

	.main-header {
		padding: 0;
		top:0;

		#nav-trigger {
			display: none;
		}

		.logo-wrapper {
			position: static;
			height: 1rem;
			margin: 0.5rem 15px;
			width: 60px;

			embed,
			img {
				height: 1rem;
			}
		}

		nav {
			padding: 0.5rem 0;
			text-align: right;
			margin-right: 15px;

			li {
				display: inline-block;
			}

			a {
				font-size: 0.7rem;
			}
		}
	}
}

@include media-breakpoint-up(lg) {

	.main-header {

		.logo-wrapper {

			embed,
			img {
				position: relative;
				z-index: -1;
			}
		}
	}
}

.home .main-header .logo-wrapper{
	-webkit-transition: all .3s ease;
			transition: all .3s ease;
	-webkit-transform: translate(0,-50px);
		-ms-transform: translate(0,-50px);
			transform: translate(0,-50px);
  }
  .home .main-header .logo-wrapper.active{
	-webkit-transform: none;
		-ms-transform: none;
			transform: none;
  }