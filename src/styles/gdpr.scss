.cc--darkmode {
    --cc-bg: #252422;
    --cc-primary-color: #fff;
    --cc-secondary-color: #fff;

    #cc-main {
        /** Change font **/
        /* --cc-font-family: Roboto; */
        --cc-bg: #252422;
        --cc-secondary-color: #fff;

        /** Change button primary color to black **/
        --cc-btn-primary-bg: #252422;
        --cc-btn-primary-color: #c4c2ba;
        --cc-btn-primary-border-color: #c4c2ba;
        --cc-btn-primary-hover-bg: #252422;
        --cc-btn-primary-hover-color: #595652;
        --cc-btn-primary-hover-border-color: #595652;

        --cc-btn-secondary-bg: #252422;
        --cc-btn-secondary-color: #c4c2ba;
        --cc-btn-secondary-border-color: #c4c2ba;
        --cc-btn-secondary-hover-bg: #252422;
        --cc-btn-secondary-hover-color: #595652;
        --cc-btn-secondary-hover-border-color: #595652;

        /** Also make toggles the same color as the button **/
        --cc-toggle-on-bg: var(--cc-btn-primary-bg);

        /** Make the buttons a bit rounder **/
        --cc-btn-border-radius: 0;

        .toggle__icon-off,
        .toggle__icon-on {
            line-height: 1.75;
        }

        .cm--cloud {
            .cm__body {
                flex-direction: column;
                align-items: center;
            }
            .cm__desc {
                text-align: center;
                padding-bottom: 0.5em;
            }
        }

        .cm__btns {
            border: none !important;
        }
    }
}
