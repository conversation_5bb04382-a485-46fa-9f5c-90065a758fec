---
const { items, menuClass, itemClass='', isMain=false } = Astro.props
const pathname = Astro.url.pathname

---
<nav id="primary-navigation" class={`${menuClass}`}>
  <ul class="menu">
    {items && items.nodes.map((item, index) => (
    <li class={item.cssClasses ? item?.cssClasses.join(" "):''}>
      <a
      href={item.uri}
      class={`${itemClass}${Astro.url == item.uri ? ' current-menu-item':''}`}
      target={item.target}
      set:html={ item.title }
    ></a>
    </li>
    ))}
    <slot />
  </ul>
</nav>
