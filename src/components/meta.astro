---
const {seo} = Astro.props
if( !seo ) return
const siteDomain = process.env.SITE_DOMAIN || "heartwoodinns.com",
      api_url = process.env.WORDPRESS_API_URL || false
const replace = api_url ? api_url.substring(0, api_url.lastIndexOf('/')) : null,
      canonical = seo.canonical ? seo.canonical.replaceAll(replace, 'https://'+siteDomain) : 'https://'+siteDomain
let   schemaRaw = null

seo.opengraphUrl = 'https://'+siteDomain

// --- fix Yoast url's
if( api_url ) {
  let replace = api_url.substring(0, api_url.lastIndexOf('/'))
  schemaRaw = seo.schema.raw.replaceAll(replace, 'https://'+siteDomain)
}else {
  schemaRaw = seo.schema.raw
}
---
<Fragment>
  {/* Yoast SEO */}
  {seo && (
    <>
      <meta name="robots" content={`${seo.metaRobotsNoindex}, ${seo.metaRobotsNofollow}`} />
      <link rel="sitemap" href="/sitemap-index.xml" />
      {canonical && <link rel="canonical" href={canonical} />}
      {seo.opengraphDescription && <meta name="description" content={seo.opengraphDescription}/>}
      {seo.opengraphImage && <meta property="og:image" content={seo.opengraphImage.sourceUrl}/>}
      <meta property="og:locale" content="en_GB" />
      <meta property="og:type" content="article" />
      <meta property="og:title" content={seo.title} />
      {seo.opengraphDescription && <meta property="og:description" content={seo.opengraphDescription} />}
      <meta property="og:url" content={seo.opengraphUrl.replaceAll(replace, 'https://'+siteDomain)} />
      <meta property="og:site_name" content={seo.opengraphSiteName} />
      {seo.opengraphModifiedTime && <meta property="article:modified_time" content={seo.opengraphModifiedTime} />}
      {schemaRaw && <script type="application/ld+json" class="yoast-schema-graph" set:html={schemaRaw}></script>}
    </>
  )}
</Fragment>