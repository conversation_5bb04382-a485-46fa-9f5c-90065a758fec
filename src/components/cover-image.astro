---
import { Image } from 'astro:assets'

const { title, coverImage, slug } = Astro.props
const width = coverImage.node.mediaDetails.sizes ? coverImage.node.mediaDetails.sizes[0].width : coverImage.node.mediaDetails.width,
      height = coverImage.node.mediaDetails.sizes ? coverImage.node.mediaDetails.sizes[0].height : coverImage.node.mediaDetails.height
---
<div class="mx-0">
    {slug ? (
      <a href={`/our-work/${slug}`} aria-label={title}>
        <Image
            width={width}
            height={height}
            alt={`Cover Image for ${title}`}
            src={coverImage?.node.sourceUrl}
            class={'img-fluid'}
            />
      </a>
    ) : (
        <Image
        width={width}
        height={height}
        alt={`Cover Image for ${title}`}
        src={coverImage?.node.sourceUrl}
        class={'img-fluid'}
        />
    )}
</div>