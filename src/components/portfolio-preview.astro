---
import CoverImage from "@components/cover-image.astro"

const {
    title,
    coverImage,
    slug,
    tags = null,
    // client,
    // design
} = Astro.props

// const clientLogo = client ? client[0].details.clientLogo : null
let tagsString = tags && tags.nodes.reduce(
    (acc, val) => acc + ' ' + val.slug.toLowerCase(), ''
  )
---
<div class={`project masonry-item ${tagsString}`}>
    <div class="project-wrapper">

      <CoverImage postType="portfolios" title={title} coverImage={coverImage} slug={slug} />

      <a class="project-arrow" href={`/our-work/${slug}`} aria-label={title}>&nbsp;</a>
    </div>
</div>
