---
import Header from "@components/Header.astro"
import Footer from "@components/Footer.astro"
import Meta from "@components/meta.astro"
import Favicons from "@components/favicons.astro"
import { getMenuItemsByLocation, wpSettings } from "../lib/api"
import CookieConsent from "@components/cookie-consent.astro"
import '../styles/index.scss';

const {pageTitle, bodyClass='', layout='', seo} = Astro.props
const settings = await wpSettings()
const mainMenu = await getMenuItemsByLocation('PRIMARY_NAVIGATION')
const footerMenu = await getMenuItemsByLocation('FOOTER_NAVIGATION')
const PUBLIC_RECAPTCHA_KEY = import.meta.env.PUBLIC_RECAPTCHA_KEY
---

<html lang="en">
	<head>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<meta name="generator" content={Astro.generator} />
		<title>{pageTitle} - Astro</title>
		<Favicons />
		{seo && <Meta seo={seo} />}
        <CookieConsent />
		<link rel="preconnect" href="https://www.google.com" />
	</head>
	<body class={bodyClass}>
		<main class='main-content'>
		{layout == 'clean' ? (
			<slot />
		) : (
			<Header menu={mainMenu} />
			<slot />
			<Footer menu={footerMenu} />
		)}
		</main>
	</body>

	<script>
      // Boostrap javascript: Import only what you need
      // import 'bootstrap/js/dist/alert';
      // import 'bootstrap/js/dist/button';
      // import 'bootstrap/js/dist/carousel';
      // import 'bootstrap/js/dist/collapse';
      // import 'bootstrap/js/dist/dropdown';
      // import 'bootstrap/js/dist/modal';
      // import 'bootstrap/js/dist/offcanvas';
      // import 'bootstrap/js/dist/popover';
      // import 'bootstrap/js/dist/scrollspy';
      // import 'bootstrap/js/dist/tab';
      // import 'bootstrap/js/dist/toast';
      // import 'bootstrap/js/dist/tooltip';

	  // Import everything (this will import all components)
	  // import 'bootstrap/dist/js/bootstrap';
    </script>

	<script is:inline define:vars={{PUBLIC_RECAPTCHA_KEY}}>

		// === attach submit event to all WPCF7 forms
		const wpcf7Forms = document.querySelectorAll('.wpcf7-form')
		if( wpcf7Forms ) {
			console.log('found some WPCF7 forms: ', wpcf7Forms.length)

			function onSubmit(e) {
				e.preventDefault()
				const formEl = e.target
				const validationTips = formEl.querySelectorAll('.wpcf7-not-valid-tip')
				const responseEl = formEl.querySelector('.wpcf7-response-output')
				const id = formEl.querySelector('[name="_wpcf7"]')?.value
				console.log('form id: ' + id)
				if( id && "function" === typeof grecaptcha?.ready ) {
					let formData = new FormData(formEl)
					// for ( const pair of formData.entries() ){
					//   console.log(`${pair[0]}, ${pair[1]}`)
					// }
					if( validationTips.length ) validationTips.forEach(e => e.remove())
					grecaptcha.ready(function() {
						grecaptcha.execute(PUBLIC_RECAPTCHA_KEY, {action: 'submit'}).then(function(token) {
							console.log(token)
							// Add your logic to submit to your backend server here.
							// --- use reCaptcha
							fetch("/api/recaptcha?", {
								method: "POST",
								body: JSON.stringify({ recaptcha: token })
							})
							.then((response) => response.json())
							.then((gResponse) => {
								if (gResponse.success) {
									// Captcha verification was a success
									console.log(gResponse)
									fetch("/api/wpcf7?"+ new URLSearchParams({id: id}), {
										method: "POST",
										body: formData
									})
									.then((response) => response.json())
									.then((WPCF7response) => {
										// console.log(WPCF7response)
										// success
										if( WPCF7response.status == 'mail_sent' ) {
											console.log('Submission sent')
											formEl.className = 'wpcf7-form sent'
											responseEl.innerHTML = WPCF7response.message
											formEl.reset()
										}else if( WPCF7response.status == 'validation_failed' ) {
											console.log('Validation failed')
											formEl.className = 'wpcf7-form invalid'
											responseEl.innerHTML = WPCF7response.message
											if( WPCF7response.invalid_fields.length ) {
												WPCF7response.invalid_fields.forEach((item)=>{
												// console.log(item)
												formEl.querySelector(`[name="${item.field}"]`).parentNode.insertAdjacentHTML('afterend',`<span class="wpcf7-not-valid-tip" aria-hidden="true">${item.message}</span>`)
											})
											}
										}else {
											console.log('WPCF7 response failed')
											formEl.className = 'wpcf7-form failed'
											responseEl.innerHTML = WPCF7response.message
										}
									})
								} else {
									// Captcha verification failed
									console.log('reCaptcha call failed')
									console.log(gResponse)
									responseEl.innerHTML = gResponse?.message
									return
								}
							})
						});
					});
				}
			}

			wpcf7Forms.forEach((item)=>{
				item.addEventListener('submit', onSubmit)
			})

			// === Load reCaptcha
			window.addEventListener("load", ()=>{
				let reCaptchaLoaded = false;
				// console.log('document loaded...')
				if (!reCaptchaLoaded) {
					var recaptchaScript = document.createElement('script');
					recaptchaScript.setAttribute('src', `https://www.google.com/recaptcha/api.js?render=${PUBLIC_RECAPTCHA_KEY}`);
					document.body.appendChild(recaptchaScript);
					reCaptchaLoaded = true;
				}

			})
		}

	  </script>
</html>

<script>
import { scrollIntoView } from "seamless-scroll-polyfill";
const handleSmoothClick = (e) => {
	const { tagName, href } = e.target;
	if( ['a'].indexOf(tagName.toLowerCase()) != -1 && href.indexOf('#') != -1 ) {
	let id = href.slice(href.indexOf('#')),
		targetEl = document.getElementById(id.slice(1))
	if( targetEl ) {
		e.preventDefault()
		// e.stopPropagation()
		// console.log('anchor link clicked...', tagName, href.slice(href.indexOf('#')))
		scrollIntoView(targetEl, { behavior: "smooth", block: "start" })
	}
	}
}
document.addEventListener('click', handleSmoothClick)
</script>