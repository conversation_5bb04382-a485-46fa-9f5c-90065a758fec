import { defineConfig, passthroughImageService } from 'astro/config';
import vercel from "@astrojs/vercel/serverless";
import sitemap from '@astrojs/sitemap';

// const SITE_URL = import.meta.env.SITE_URL || "https://saintdesign.co.uk"

// https://astro.build/config
export default defineConfig({
  output: "server",
  site: "https://saintdesign.co.uk",
  integrations: [sitemap()],
  image: {
    domains: ["cms.saintdesign.co.uk"],
    // service: passthroughImageService(),
  },
  adapter: vercel({
    isr: {
      // caches all pages on first request and saves for 5 minute
      expiration: 60 * 5, // in seconds
      // A secret random string that you create.
      bypassToken: "saint-secret-phrase-hard2guess-letmein2024",
      // Paths that will always be served fresh.
      // exclude: [ "/api/invalidate", "/posts/[...slug]" ]
    },
    imageService: true,
    devImageService: 'sharp',
    imagesConfig: {
      sizes: [320, 640, 1024],
      domains: ["cms.saintdesign.co.uk"]
    },
    // edgeMiddleware: true,
  }),
  redirects: {
    "/sitemap.xml" : "/sitemap-index.xml"
  }
});