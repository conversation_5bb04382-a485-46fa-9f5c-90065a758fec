---
import {getAllPortfoliosForHome} from '../../lib/api'
import BaseLayout from "../../layouts/BaseLayout.astro"
import BlogPost from "@components/BlogPost.astro"

const pageTitle = 'Our work'
const data = await getAllPortfoliosForHome()
const portfolios = data
console.log(portfolios)

---

<BaseLayout pageTitle={pageTitle} >
    <ul class="archive-list">
        {portfolios.map((item) => <BlogPost post={item.node} />)}
    </ul>
</BaseLayout>