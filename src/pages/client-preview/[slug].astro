---
import BaseLayout from '../../layouts/BaseLayout.astro';
import { getClientPreview, getAllClientPreviewsWithSlug } from 'src/lib/api';
import { Image } from 'astro:assets';

export async function getStaticPaths() {
  const postsWithSlugs = await getAllClientPreviewsWithSlug()
  return postsWithSlugs.edges.map(({ node }) => {
    return {
      params: { slug: node.slug },
    };
  });
}

const { slug } = Astro.params
const data = await getClientPreview(slug)
const post = data.clientPreview
const featuredImage = post?.featuredImage?.node || false

---

<!-- <BaseLayout pageTitle={post.title} seo={post.seo} bodyClass="single-folio single-folio-grid"> -->
  <!-- <PasswordProtected ctx={post}> -->
    {featuredImage && <img src={featuredImage.sourceUrl} width={featuredImage.mediaDetails.width} height={featuredImage.mediaDetails.height} alt={post.title} />}
  <!-- </PasswordProtected> -->
<!-- </BaseLayout> -->