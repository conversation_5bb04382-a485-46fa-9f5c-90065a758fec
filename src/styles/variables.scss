// Optional variable overrides
$green: #2AB566;
$yellow: #F3FF00;
$black: #1C1C23;
$light: #E5E0DA;

$primary: $green;
$secondary: $yellow;

$prefix:                      bs- !default;

$theme-colors: (
    "primary": $primary,
    "secondary": $secondary,
    "black": $black,
    "yellow": $yellow,
    "green": $green,
    "light": $light
) !default;

// scss-docs-start grid-breakpoints
$grid-breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px
) !default;
// scss-docs-end grid-breakpoints

// Grid containers
//
// Define the maximum width of `.container` for different screen sizes.

// scss-docs-start container-max-widths
$container-max-widths: (
  sm: 100%,
  md: 738px,
  lg: 932px,
  xl: 1100px,
  xxl: 1100px
) !default;
// scss-docs-end container-max-widths

// Grid columns
//
// Set the number of columns and specify the width of the gutters.
$grid-gutter-width:           1rem !default;

// scss-docs-start spacer-variables-maps
$spacer: (10 / 16) * 1rem !default;
$spacers: (
  0: 0,
  1: $spacer,
  2: $spacer * 2,
  3: $spacer * 3,
  4: $spacer * 4,
  5: $spacer * 5,
  6: $spacer * 6,
  7: $spacer * 7,
  8: $spacer * 8,
  9: $spacer * 9,
  10: $spacer * 10
) !default;

// stylelint-disable function-disallowed-list
// scss-docs-start aspect-ratios
$aspect-ratios: (
  "1x1": 100%,
  "2x1": calc(1 / 2 * 100%),
  "4x3": calc(3 / 4 * 100%),
  "9x16": calc(16 / 9 * 100%),
  "9x13": calc(13 / 9 * 100%),
  "16x9": calc(9 / 16 * 100%),
  "21x9": calc(9 / 21 * 100%),
) !default;
// scss-docs-end aspect-ratios
// stylelint-enable function-disallowed-list

// Links
//
// Style anchor elements.

$link-color:                              $primary !default;
$link-decoration:                         none !default;
$link-shade-percentage:                   20% !default;
$link-hover-color:                        shift-color($link-color, $link-shade-percentage) !default;
$link-hover-decoration:                   underline !default;

// Paragraphs
//
// Style p element.

$paragraph-margin-bottom:   (30 / 16) * 1rem !default;

// scss-docs-start border-radius-variables
$border-radius:               (6 / 16) * 1rem !default;

$transition-base:             all .2s ease-in-out !default;
$transition-fade:             opacity .15s linear !default;

// Typography
//
// Font, line-height, and color for body text, headings, and more.

// scss-docs-start font-variables
// stylelint-disable value-keyword-case
$font-family-sans-serif:      Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji" !default;
$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace !default;
// stylelint-enable value-keyword-case
$font-family-base:            var(--#{$prefix}font-sans-serif) !default;

// $font-size-root affects the value of `rem`, which is used for as well font sizes, paddings, and margins
// $font-size-base affects the font size of the body text
$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`
$font-size-sm:                $font-size-base * .875 !default;
$font-size-lg:                $font-size-base * 1.25 !default;

$line-height-base:            (26 / 16) !default;
$line-height-sm:              1.25 !default;
$line-height-lg:              2 !default;

$h1-font-size:                (45 / 16) * 1rem !default;
$h2-font-size:                (35 / 16) * 1rem !default;
$h3-font-size:                (25 / 16) * 1rem !default;
$h4-font-size:                (20 / 16) * 1rem !default;
$h5-font-size:                (20 / 16) * 1rem !default;
$h6-font-size:                (18 / 16) * 1rem !default;
// scss-docs-end font-variables