import { addYears } from 'date-fns'

const secret = import.meta.env.BASIC_AUTH_PASSWORD || false
const maintenance = import.meta.env.VERCEL_MAINTENANCE || 'test'
const VERCEL_ENV = import.meta.env.VERCEL_ENV || 'local'

export function onRequest ({ request, redirect, cookies }, next) {
    const basicAuth = request.headers.get('authorization')
    let url = new URL(request.url)

    // === Maintenance mode
    // console.log(request.headers)
    if( maintenance == "yes" && url.pathname !== '/maintenance' ) {
        url.pathname = `/maintenance`
        return redirect(url)
    }

    // === Basic Authorisation
    // console.log(url.pathname, VERCEL_ENV, secret, basicAuth, cookies.get('let-me-in'))
    if ( VERCEL_ENV == 'local' || !secret || ( cookies.get('let-me-in') && secret === cookies.get('let-me-in').value) ) {
        // console.log('authorized, please continue...')
        return next()
    }

    if (basicAuth) {
        const authValue = basicAuth.split(' ')[1]
        const [user, pwd] = atob(authValue).split(':')
        // console.log('authorisation process...')
        if (user === 'letmein' && pwd === secret) {
            const expire = addYears(new Date(), 1)
            cookies.set(
                'let-me-in',
                secret,
                {
                    domain: url.hostname,
                    secure: true,
                    httpOnly: true,
                    expires: expire
                }
            )
            return next()
        }
    }

    // console.log('we need authorisation...', url.pathname)
    return new Response("Auth required", {
        status: 401,
        headers: {
          "WWW-authenticate": 'Basic realm="Secure Area"',
        },
    });
};