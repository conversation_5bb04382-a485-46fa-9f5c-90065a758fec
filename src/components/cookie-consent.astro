---
---
<Fragment>
    <script set:html={`window.dataLayer = window.dataLayer || []
        function gtag() { dataLayer.push(arguments) }
        gtag('consent', 'default', {
            ad_user_data: 'denied',
            ad_personalization: 'denied',
            ad_storage: 'denied',
            analytics_storage: 'denied',
            wait_for_update: 500
        })
        gtag('js', new Date())
        gtag('config', 'UA-106472963-2')`
    }>
    </script>
    <script
        type="text/plain"
        data-category="statistics"
        data-service="ga"
        set:html={ `function gtag(){dataLayer.push(arguments)}
            gtag('consent', 'update', {
                ad_user_data: 'granted',
                ad_personalization: 'granted',
                ad_storage: 'granted',
                analytics_storage: 'granted'
            })`
        }>
    </script>
    <script
        async
        type="text/plain"
        data-category="statistics"
        data-service="ga"
        src="https://www.googletagmanager.com/gtag/js?id=UA-106472963-2"></script>
</Fragment>

<script>
    import * as CookieConsent from "vanilla-cookieconsent"
    const SITE_URL = import.meta.env.SITE_URL

    if ( !document.getElementById('cc--main') ) {
            document.documentElement.classList.add('cc--darkmode')
            CookieConsent.run({

                // root: 'body',
                // autoShow: true,
                // disablePageInteraction: true,
                // hideFromBots: true,
                // mode: 'opt-in',
                // revision: 0,

                cookie: {
                    name: 'cc_cookie',
                    // domain: location.hostname,
                    // path: '/',
                    sameSite: "Lax",
                    expiresAfterDays: 365,
                },

                // https://cookieconsent.orestbida.com/reference/configuration-reference.html#guioptions
                guiOptions: {
                    consentModal: {
                        layout: 'cloud inline',
                        position: 'bottom center',
                        equalWeightButtons: true,
                        flipButtons: false
                    },
                    preferencesModal: {
                        layout: 'box',
                        equalWeightButtons: true,
                        flipButtons: false
                    }
                },

                onFirstConsent: ({cookie}) => {
                    console.log('onFirstConsent fired',cookie);
                },

                onConsent: ({cookie}) => {
                    console.log('onConsent fired!', cookie)
                },

                onChange: ({changedCategories, changedServices}) => {
                    console.log('onChange fired!', changedCategories, changedServices);
                },

                onModalReady: ({modalName}) => {
                    console.log('ready:', modalName);
                },

                onModalShow: ({modalName}) => {
                    console.log('visible:', modalName);
                },

                onModalHide: ({modalName}) => {
                    console.log('hidden:', modalName);
                },

                categories: {
                    necessary: {
                        enabled: true,  // this category is enabled by default
                        readOnly: true  // this category cannot be disabled
                    },
                    statistics: {
                        autoClear: {
                            cookies: [
                                {
                                    name: /^_ga/,   // regex: match all cookies starting with '_ga'
                                },
                                {
                                    name: '_gid',   // string: exact cookie name
                                }
                            ]
                        },

                        // https://cookieconsent.orestbida.com/reference/configuration-reference.html#category-services
                        services: {
                            ga: {
                                label: 'Google Analytics',
                                onAccept: () => { console.log('Google Analytics ENABLED') },
                                onReject: () => { console.log('Google Analytics DISABLED') }
                            }
                        }
                    },
                },

                language: {
                    default: 'en',
                    translations: {
                        en: {
                            consentModal: {
                                title: null,
                                description: 'This website is using cookies. Please let us know if that\'s ok or change your settings.',
                                acceptAllBtn: 'That\'s OK',
                                // acceptNecessaryBtn: 'Reject all',
                                showPreferencesBtn: 'Change Settings',
                                // closeIconLabel: 'Reject all and close modal',
                                // footer: `
                                //     <a href="/cookie-policy/" target="_blank">Cookie Policy</a>
                                //     <a href="/privacy-policy/" target="_blank">Privacy Policy</a>
                                // `,
                            },
                            preferencesModal: {
                                title: 'Manage cookie preferences',
                                acceptAllBtn: 'Accept All',
                                // acceptNecessaryBtn: 'Reject all',
                                savePreferencesBtn: 'Save Preferences',
                                closeIconLabel: 'Close modal',
                                serviceCounterLabel: 'Service|Services',
                                sections: [
                                    {
                                        title: 'Your Privacy Choices',
                                        description: `In this panel you can express some preferences related to the processing of your personal information. You may review and change expressed choices at any time by resurfacing this panel via the provided link. To deny your consent to the specific processing activities described below, switch the toggles to off or use the “Reject all” button and confirm you want to save your choices.<br>Before you proceed please review our <a href="${SITE_URL}/privacy-policy" target="_blank">privacy policy</a>.`,
                                    },
                                    {
                                        title: 'Strictly Necessary',
                                        description: 'Necessary cookies help make a website usable by enabling basic functions like page navigation and access to secure areas of the website. The website cannot function properly without these cookies.',

                                        //this field will generate a toggle linked to the 'necessary' category
                                        linkedCategory: 'necessary'
                                    },
                                    {
                                        title: 'Statistic',
                                        description: 'Statistic cookies help website owners to understand how visitors interact with websites by collecting and reporting information anonymously.',
                                        linkedCategory: 'statistics',
                                        cookieTable: {
                                            caption: 'Cookie table',
                                            headers: {
                                                name: 'Cookie',
                                                domain: 'Provider',
                                                desc: 'Purpose',
                                                expiry: 'Expiry',
                                                type: 'Type',
                                            },
                                            body: [
                                                {
                                                    name: '_ga',
                                                    domain: location.hostname,
                                                    desc: 'Registers a unique ID that is used to generate statistical data on how the visitor uses the website.',
                                                    expiry: '2 years',
                                                    type: 'HTTP',
                                                },
                                                {
                                                    name: '_gat',
                                                    domain: location.hostname,
                                                    desc: 'Used by Google Analytics to throttle request rate',
                                                    expiry: '1 day',
                                                    type: 'HTTP',
                                                },
                                                {
                                                    name: '_gid',
                                                    domain: location.hostname,
                                                    desc: 'Registers a unique ID that is used to generate statistical data on how the visitor uses the website.',
                                                    expiry: '1 day',
                                                    type: 'HTTP',
                                                }
                                            ]
                                        }
                                    },
                                    // {
                                    //     title: 'More information',
                                    //     description: `For any queries in relation to our policy on cookies and your choices, please <a href="${SITE_URL}/#contact-us">contact us</a>`
                                    // }
                                ]
                            }
                        }
                    }
                }
            });
        }

        // const cookieContent = CookieConsent.getCookie()
        // console.log(cookieContent)

        const showPrefBtn = document.querySelector('[data-cc="show-preferencesModal"]')
        const showPrefBtnCallback = function(){
            console.log('show pref btn clicked...')
            CookieConsent.showPreferences()
        }
        if( showPrefBtn && CookieConsent ) {
            showPrefBtn.addEventListener('click', showPrefBtnCallback)
        }
</script>