---
import { getAllPagesWithSlug, getPageBySlug } from "src/lib/api";
import BaseLayout from "../layouts/BaseLayout.astro"
import PageHeader from "@components/page-header.astro"
import PostBody from "@components/post-body.astro"

export async function getStaticPaths() {
  const postsWithSlugs = await getAllPagesWithSlug()
  return postsWithSlugs.edges.map(({ node }) => {
    return {
      params: { slug: node.slug },
    };
  });
}

const { slug } = Astro.params
const data = await getPageBySlug(slug)
const page = data.page

---
<BaseLayout pageTitle={page.title} seo={page.seo}>
  <article class='page-wrap'>
    <!-- <PasswordProtected ctx={page}> -->
      <PageHeader
        title={page.title}
      />
      <PostBody content={page.content} />
    <!-- </PasswordProtected> -->
  </article>
</BaseLoayout>