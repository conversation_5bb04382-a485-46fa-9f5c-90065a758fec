@keyframes faded {
    0% {opacity: 0;}
    50% {opacity: 1;}
    100% {opacity:0;}
}

@keyframes fadeIn {
    0% {opacity: 0;}
    25% {opacity:0}
    100% {opacity: 1;}
}

@keyframes fadeInLeft {
    0%, 100% {
        transition-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
    }
    0% {
        opacity: 0;
        transform: translate3d(-40px, 0, 0);
    }
    100% {
        transform: none;
    }
 }

@keyframes fadeInRight {
    0%, 100% {
        transition-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
    }
    0% {
        opacity: 0;
        transform: translate3d(40px, 0, 0);
    }
    100% {
        transform: none;
    }
}

@keyframes fadeInUp {
    0%, 100% {
        transition-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
    }
    0% {
        opacity: 0;
        transform: translate3d(0,40px,0);
    }
    100% {
        transform :none;
    }
}

@keyframes slideUp {
    0%, 100% {
        transition-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
    }
    0% {
        opacity: 1;
        transform: translate3d(0,40px,0);
    }
    100% {
        opacity: 1;
        transform: none;
    }
}

@keyframes slideUpBig {
    0%, 100% {
        transition-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
    }
    0% {
        opacity: 1;
        transform: translate3d(0,80px,0);
    }
    100% {
        opacity: 1;
        transform: none;
    }
}

.fadeIn {
    animation-name: fadeIn;
}
.fadeInUp{
    animation-name: fadeInUp;
}
.fadeInLeft{
    animation-name: fadeInLeft;
}
.fadeInRight{
    animation-name: fadeInRight;
}
.slideUp{
    animation-name: slideUp;
}
.slideUpBig{
    animation-name: slideUpBig;
}

.animated{
    animation-duration: 1000ms;
    animation-fill-mode: both
}
.animated.fadeIn{
    animation-duration: 1s;
}

.loading {
    position: absolute;
    left: 50%;
    top: 50%;
    text-align: center;
    transform: translateX(-50%);
    animation-duration: 2s;
    animation-name: faded;
    animation-iteration-count: infinite;
}