---
import BaseLayout from '../../layouts/BaseLayout.astro';
import { getAllPortfoliosWithSlug, getPortfolioAndRelated } from 'src/lib/api';
import Container from '@components/container.astro';
import Tags from '@components/tags.astro';

export async function getStaticPaths() {
  const postsWithSlugs = await getAllPortfoliosWithSlug()
  return postsWithSlugs.edges.map(({ node }) => {
    return {
      params: { slug: node.slug },
    };
  });
}

const { slug } = Astro.params
const data = await getPortfolioAndRelated(slug)
const post = data.portfolio
---

<BaseLayout pageTitle={post?.title} seo={post?.seo} bodyClass="single-folio single-folio-grid">
  <!-- <PasswordProtected ctx={post}> -->
    <article class={`grid-wrap`}>
      <!-- TODO: Hero panel -->

      <div class="grid-wrap-inner">
        <Container>
          <!-- TODO: Intro -->
          <h1>{post.title}</h1>
          <Tags tags={post.tags} />

          <!-- TODO: Content -->

          <!-- TODO: Related -->
          {/* {related && related.length > 0 && <PortfolioRelated title="Related Projects" posts={related} />} */}
        </Container>
      </div>
    </article>
  <!-- </PasswordProtected> -->
</BaseLayout>