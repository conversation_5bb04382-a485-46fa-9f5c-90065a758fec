const API_URL = import.meta.env.WORDPRESS_API_URL
const REST_API_URL = import.meta.env.PUBLIC_WORDPRESS_REST_API
const WORDPRESS_AUTH_REFRESH_TOKEN = import.meta.env.WORDPRESS_AUTH_REFRESH_TOKEN

async function fetchAPI(query = '', { variables } = {}) {
    const headers = { 'Content-Type': 'application/json' }

    if (WORDPRESS_AUTH_REFRESH_TOKEN) {
      headers[
        'Authorization'
      ] = `Bearer ${WORDPRESS_AUTH_REFRESH_TOKEN}`
    }

    // WPGraphQL Plugin must be enabled
    const res = await fetch(API_URL, {
      headers,
      method: 'POST',
      body: JSON.stringify({
        query,
        variables,
      }),
    })

    const json = await res.json()
    if (json.errors) {
      console.error(json.errors)
      throw new Error('Failed to fetch API')
    }
    return json.data
}

export async function fetchRestAPI(endpoint, formData) {
  let myHeaders = new Headers();

  const res = await fetch(REST_API_URL+endpoint, {
    method: 'POST',
    headers: myHeaders,
    body: formData,
    // redirect: 'follow'
  })

  const json = await res.json()
  if (json.errors) {
    console.error(json.errors)
    throw new Error('Failed to fetch API')
  }
  return json
}

export async function getPreviewPost(id, idType = 'DATABASE_ID', cpt='post') {
  let cptIdType
  switch (cpt) {
    case 'page':
      cptIdType = 'PageIdType'
      break;
    case 'portfolio':
      cptIdType = 'PortfolioIdType'
      break;
    case 'clientPreview':
      cptIdType = 'ClientPreviewIdType'
      break;
    default:
      cptIdType = 'PostIdType'
      break;
  }

  const data = await fetchAPI(
    `
    query PreviewPost($id: ID!, $idType: ${cptIdType}!) {
      ${cpt}(id: $id, idType: $idType) {
        databaseId
        slug
        status
      }
    }`,
    {
      variables: { id, idType },
    }
  )
  return data[cpt]
}

export async function wpSettings() {
  const data = await fetchAPI(`
  query GetSettings {
    general: generalSettings {
        description
        language
        title
        url
    }
    reading: readingSettings {
      pageForPosts
      pageOnFront
      postsPerPage
    }
    headlessConfig {
      frontendUrl
    }
  }
  `)
  return data
}

export async function getMenuItemsByLocation(location) {
  const data = await fetchAPI(
    `
    query MENU_ITEMS($location: MenuLocationEnum) {
      menuItems(first:999, where: {location: $location}) {
        nodes {
          key: id
          parentId
          title: label
          uri
          desc: description
          target
          cssClasses
        }
      }
    }
  `,
  {
    variables: {
      location: location
    }
  }
  )

  return data.menuItems
}

export async function getAllPortfoliosWithSlug() {
  const data = await fetchAPI(`
    {
      portfolios(first: 10000) {
        edges {
          node {
            slug
          }
        }
      }
    }
  `)
  return data?.portfolios
}

export async function getAllClientPreviewsWithSlug() {
  const data = await fetchAPI(`
    {
      clientPreviews(first: 10000) {
        edges {
          node {
            slug
          }
        }
      }
    }
  `)
  return data?.clientPreviews
}

export async function getAllPagesWithSlug() {
  const data = await fetchAPI(`
    {
      pages(first: 10000) {
        edges {
          node {
            slug
          }
        }
      }
    }
  `)
  return data?.pages
}

export async function getPortfolioAndRelated(slug, preview=false, previewData=false) {
  const postPreview = preview && previewData?.post
  // The slug may be the id of an unpublished post
  const isId = Number.isInteger(Number(slug))
  const isSamePost = isId
    ? Number(slug) === postPreview.id
    : slug === postPreview?.slug
  const isDraft = isSamePost && postPreview?.status === 'draft'
  const isRevision = isSamePost && postPreview?.status === 'publish'
  const data = await fetchAPI(
    `
    query PostBySlug($id: ID!, $idType: PortfolioIdType!) {
      portfolio(id: $id, idType: $idType) {
        id
        title
        slug
        status
        postPassword
        seo {
          canonical
          title
          metaRobotsNoindex
          metaRobotsNofollow
          opengraphTitle
          opengraphDescription
          opengraphImage {
            sourceUrl
          }
          opengraphUrl
          opengraphSiteName
          opengraphModifiedTime
          schema {
              raw
          }
        }
        tags {
          edges {
            node {
              name
            }
          }
        }
      }
    }
  `,
    {
      variables: {
        id: isDraft ? postPreview.id : slug,
        idType: isDraft ? 'DATABASE_ID' : 'SLUG',
      },
    }
  )

  // Draft posts may not have an slug
  if (isDraft) data.portfolio.slug = postPreview.id

  // // Filter out the main post
  // data.portfolios.edges = data.portfolios.edges.filter(({ node }) => node.slug !== slug)
  // // If there are still 3 posts, remove the last one
  // if (data.portfolios.edges.length > 2) data.portfolios.edges.pop()

  return data
}

export async function getAllPortfoliosForHome(preview) {
  const data = await fetchAPI(
    `
    query AllPosts {
      portfolios(first: 99, where: { orderby: { field: MENU_ORDER, order: ASC } }) {
        edges {
          node {
            title
            slug
            date
            tags {
              nodes {
                slug
              }
            }
            featuredImage {
              node {
                sourceUrl(size: MEDIUM_LARGE)
                mediaDetails {
                  sizes(include: MEDIUM_LARGE) {
                    width
                    height
                  }
                  width
                  height
                }
              }
            }
          }
        }
      }
    }
  `,
    {
      variables: {
        onlyEnabled: !preview,
        preview,
      },
    }
  )

  return data?.portfolios?.edges
}

export async function getPageBySlug(slug, preview, previewData) {
  const pagePreview = preview && previewData?.post
  // The slug may be the id of an unpublished post
  const isId = Number.isInteger(Number(slug))
  const isSamePost = isId
    ? Number(slug) === pagePreview.id
    : slug === pagePreview?.slug
  const isDraft = isSamePost && pagePreview?.status === 'draft'
  const isRevision = isSamePost && pagePreview?.status === 'publish'
  const data = await fetchAPI(
    `
    {
      page(id: "${slug}", idType: URI) {
        id
        title
        slug
        content
        status
        postPassword
        seo {
          canonical
          title
          metaRobotsNoindex
          metaRobotsNofollow
          opengraphTitle
          opengraphDescription
          opengraphImage {
            sourceUrl
          }
          opengraphUrl
          opengraphSiteName
          opengraphModifiedTime
          schema {
              raw
          }
        }
        ${
          // Only some of the fields of a revision are considered as there are some inconsistencies
          isRevision
            ? `
        revisions(first: 1, where: { orderby: { field: MODIFIED, order: DESC } }) {
          edges {
            node {
              title
              excerpt
              content
              author {
                node {
                  ...AuthorFields
                }
              }
            }
          }
        }
        `
            : ''
        }
      }
    }
  `
  )

  // Draft posts may not have an slug
  if (isDraft) data.page.slug = pagePreview.id
  // Apply a revision (changes in a published post)
  if (isRevision && data.page.revisions) {
    const revision = data.page.revisions.edges[0]?.node

    if (revision) Object.assign(data.page, revision)
    delete data.page.revisions
  }

  return data
}

export async function getHomePage() {
  const data = await fetchAPI(
    `
    {
      page(id: "/", idType: URI) {
        title
        uri
        seo {
          canonical
          title
          metaRobotsNoindex
          metaRobotsNofollow
          opengraphTitle
          opengraphDescription
          opengraphImage {
            sourceUrl
          }
          opengraphUrl
          opengraphSiteName
          opengraphModifiedTime
          schema {
              raw
          }
        }

      }
      tags(first: 99, where: {hideEmpty: true}) {
        edges {
          node {
            name
            slug
          }
        }
      }
    }
    `
  )

  return data
}

export async function getClientPreview(slug, preview, previewData) {
  const postPreview = preview && previewData?.post
  // The slug may be the id of an unpublished post
  const isId = Number.isInteger(Number(slug))
  const isSamePost = isId
    ? Number(slug) === postPreview.id
    : slug === postPreview?.slug
  const isDraft = isSamePost && postPreview?.status === 'draft'
  const isRevision = isSamePost && postPreview?.status === 'publish'
  const data = await fetchAPI(
    `
    query PostBySlug($id: ID!, $idType: ClientPreviewIdType!) {
      clientPreview(id: $id, idType: $idType) {
        id
        title
        slug
        status
        postPassword
        featuredImage {
          node {
            sourceUrl
            mediaDetails {
              width
              height
            }
          }
        }
        seo {
          canonical
          title
          metaRobotsNoindex
          metaRobotsNofollow
          opengraphTitle
          opengraphDescription
          opengraphImage {
            sourceUrl
          }
          opengraphUrl
          opengraphSiteName
          opengraphModifiedTime
          schema {
              raw
          }
        }
      }
    }
  `,
    {
      variables: {
        id: isDraft ? postPreview.id : slug,
        idType: isDraft ? 'DATABASE_ID' : 'SLUG',
      },
    }
  )

  // Draft posts may not have an slug
  if (isDraft) data.clientPreview.slug = postPreview.id

  // // Filter out the main post
  // data.portfolios.edges = data.portfolios.edges.filter(({ node }) => node.slug !== slug)
  // // If there are still 3 posts, remove the last one
  // if (data.portfolios.edges.length > 2) data.portfolios.edges.pop()

  return data
}