---
import BaseLayout from "../layouts/BaseLayout.astro"

const title = 'Maintenance page'
const body = '<h3>We\'re having an online makeover<br/> Please check back soon...</h3>'
const bodyClass = 'maintenance-page'
---
<BaseLayout pageTitle={title} bodyClass={bodyClass} layout="clean">
    <div class="maintenance-page-inner">
        <div class="entry-content text-center pb-50">
            {/* <svg class='rene' fill='currentColor' viewBox="0 0 141.73 163.12" width="130" height="150">
                <use href={`/images/icon.svg` + `#rene`} />
            </svg> */}
            <div class='d-flex flex-column justify-content-center align-items-center mb-20'>
                {title && <h1 set:html={title}></h1>}
            </div>
            <div class="body-copy mt-0" set:html={body} />
        </div>
    </div>
</BaseLoayout>