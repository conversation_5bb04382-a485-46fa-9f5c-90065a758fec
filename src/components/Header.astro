---
import Menu from './menu.astro'

const {menu} = Astro.props
---
<header id="main-header" class='main-header bg-black'>
    <div class="mx-auto px-5">
      <div class="d-flex justify-content-between align-items-center">
        <div >
          <a id="main-logo" href="/" class='logo-wrapper'>
            <img
            width={90}
            height={24}
            src='/images/saint-mid-grey.svg'
            alt='Saint logo'
            class='logo'
            />
          </a>
        </div>
        <div class='d-flex flex-column'>
          <Menu items={menu} menuClass="nav-main" isMain={true} />
          <button id="nav-trigger" >
            <span class="sr-only">Menu</span>
          </button>
        </div>
      </div>
    </div>
</header>

<script>
    const headerRef = document.getElementById('main-header')
    const logoRef = document.getElementById('main-logo')
    const navTriggerRef = document.getElementById('nav-trigger')
    const el = document.getElementById('intro_video_ctn')

    const toggleMenu = () =>  {
        if( !headerRef ) return false
        headerRef.classList.toggle('open')
    }

    const handleLogoScroll = () => {
      let offset = el ? el.offsetTop : 0;
      // console.log( window.scrollY, offset, el.offsetHeight )
      if( window.scrollY > offset + el.offsetHeight - 40 ){
        logoRef.classList.add('active')
      }else{
        logoRef.classList.remove('active')
      }
    }
    if(el) window.addEventListener('scroll', handleLogoScroll)
    navTriggerRef.addEventListener('click', toggleMenu)
</script>