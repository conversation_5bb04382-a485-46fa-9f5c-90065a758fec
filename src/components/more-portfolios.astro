---
import PortfolioPreview from '@components/portfolio-preview.astro'

const { title, posts, tags } = Astro.props
---
<section id="our-work" class='section text-center xl:text-left'>
    <h2 class="mb-8">
    { title || 'projects' }
    </h2>
    <div class='flex justify-center xl:justify-start'>
        <div class='filter-tags lg:basis-8/12 xl:basis-5/12'>
        {tags.edges.length > 0 && (
        <p class="tags mb-8 text-sm inline-block max-w-full">
            <button class='item mx-1' data-handleFilterChange="*" >all</button>
            {tags.edges.map((tag, index) => (
            <button class="item mx-2" data-handleFilterChange={tag.node.slug.toLowerCase()}>
                {tag.node.name.toLowerCase()}
            </button>
            ))}
        </p>
        )}
        </div>
    </div>
    <div id="masonry-grid" class='masonry-grid'>
        <div class="mb-6">
        {posts.map(( {node} ) => (
            <PortfolioPreview
            title={node.title}
            coverImage={node.featuredImage}
            slug={node.slug}
            tags={node.tags}
            />
        ))}
        </div>
    </div>
</section>

<script>
    import Isotope from 'isotope-layout'

    // === Isotope main
    const isotopeRef = new Isotope('#masonry-grid', {
        itemSelector: '.masonry-item',
        percentPosition: true,
        masonry: {
        columnWidth: '.masonry-item'
        }
    })

    // === Isotope Filtering
    let filterKey = '*'
    const Btns = document.querySelectorAll('[data-handleFilterChange]')
    console.log('script loaded...')
    if( Btns ) {
        console.log('buttons found...')
        Btns.forEach((item:HTMLElement)=>{
            let key = (item).dataset.handlefilterchange
            // console.log(item.dataset.handlefilterchange)
            item.addEventListener('click', (e)=>{
                let el = e.target as HTMLElement
                console.log(el)
                document.querySelectorAll('.filter-tags .item').forEach(function(item){
                    item.classList.remove('active')
                })
                el.classList.add('active')
                filterKey = key
                if( filterKey ) {
                    // console.log(filter)
                    if (filterKey === "*") isotopeRef?.arrange({ filter: `*` });
                    else isotopeRef?.arrange({ filter: `.${filterKey}` });
                }
            })
        })
    }
</script>