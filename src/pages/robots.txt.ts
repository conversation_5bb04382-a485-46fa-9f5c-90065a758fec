import { trimTrailingSlash } from "../lib/utils"

const VERCEL_ENV = import.meta.env.VERCEL_ENV || 'local'
const NOINDEX = import.meta.env.NOINDEX || 0
const SITE_URL = import.meta.env.SITE_URL || "https://saintdesign.co.uk"
let content = ''

export async function GET({params, request}) {
    if ( VERCEL_ENV === 'production' && NOINDEX == 0 )
    {
        console.log('production: ', VERCEL_ENV, NOINDEX)
        let sitemap = trimTrailingSlash(SITE_URL) + '/sitemap-index.xml';
        content = 'User-agent: *\n'
        content += 'Allow: /\n'
        content += 'Disallow: /api\n'
        content += `Sitemap: ${sitemap}\n`
    } else {
        console.log('local: ', VERCEL_ENV, NOINDEX)
        content = 'User-agent: *\n'
        content += 'Disallow: /'
    }

    return new Response(
        content
    )
}