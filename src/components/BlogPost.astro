---
import { Image } from 'astro:assets';
const {post} = Astro.props
const image = post.featuredImage.node || false
const title = post.title
const url = `/our-work/${post.slug}`
const pubDate = post.date
---
<li>
    {image && <a href={url}><Image class="img-fluid"
                            src={image.sourceUrl}
                            width={image.mediaDetails.sizes ? image.mediaDetails.sizes[0].width : image.mediaDetails.width}
                            height={image.mediaDetails.sizes ? image.mediaDetails.sizes[0].height : image.mediaDetails.height}
                            alt={title} /></a>}
    <a href={url}>{title}</a>
    <p class="excerpt" set:html={post.excerpt}></p>
</li>