---
import BaseLayout from "../layouts/BaseLayout.astro"
import Container from "@components/container.astro"
import Video from "@components/video.astro"
import MorePortfolios from "@components/more-portfolios.astro"
// import FrontpageSections from "@components/frontpage-sections.astro"
import { getAllPortfoliosForHome, getHomePage } from '../lib/api'

const pageTitle = 'Saint Design London'
const data = await getHomePage()
const morePortfolios = await getAllPortfoliosForHome()
// const allSections = data.page.frontpageSections.nodes
const tags = data.tags
const page = data.page
---

<BaseLayout pageTitle={pageTitle} seo={page.seo} bodyClass="home">
    <div class="video_container" id="intro_video_ctn">
        <Video
            url="/video/Taliwang-Loop-Off-White-Background.mp4"
            mobileUrl="/video/Taliwang-Loop-Off-White-Background-mobile.mp4" />
        <div class="action-wrapper text-center">
            <a href="#our-work" class="cd-btn">work</a>
            <a href="#about-us" class="cd-btn">us</a>
        </div>
    </div>

    <Container>
        {morePortfolios.length > 0 && <MorePortfolios title="work" posts={morePortfolios} tags={tags} />}
        <!-- {allSections.length > 0 && <FrontpageSections sections={allSections}></FrontpageSections>} -->
    </Container>
</BaseLayout>